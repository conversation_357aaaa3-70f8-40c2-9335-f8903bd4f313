#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة زر حذف الكروت الناجحة في الكرت الواحد
"""

import re
import os

def test_early_cleanup_fix():
    """اختبار إصلاح التنظيف المبكر للبيانات"""
    print("🔍 اختبار إصلاح التنظيف المبكر للبيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الإصلاح
    fix_patterns = [
        'إصلاح: عدم حذف single_card_successful_usernames هنا لأنها قد تكون مطلوبة لاحقاً',
        'سيتم تنظيف البيانات بعد إرسال التفاصيل إلى التلجرام',
        'لا نحذف single_card_successful_usernames هنا لأنها قد تكون مطلوبة في send_single_card_details_to_telegram'
    ]
    
    for pattern in fix_patterns:
        if pattern not in func_code:
            print(f"❌ نمط الإصلاح غير موجود: {pattern}")
            return False
        print(f"✅ نمط الإصلاح موجود: {pattern[:50]}...")
    
    # التحقق من عدم وجود حذف مبكر لـ single_card_successful_usernames في else block
    else_block_match = re.search(r'else:\s*self\.logger\.info.*?لم يتم حفظ الكروت الناجحة.*?(?=# حفظ الكروت الفاشلة)', func_code, re.DOTALL)
    if else_block_match:
        else_block = else_block_match.group(0)
        if 'delattr(self, \'single_card_successful_usernames\')' in else_block:
            print("❌ لا يزال هناك حذف مبكر لـ single_card_successful_usernames في else block")
            return False
        else:
            print("✅ تم إزالة الحذف المبكر لـ single_card_successful_usernames من else block")
    
    return True

def test_delayed_cleanup():
    """اختبار التنظيف المؤجل في send_single_card_details_to_telegram"""
    print("\n🔍 اختبار التنظيف المؤجل في send_single_card_details_to_telegram...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من التنظيف المؤجل
    delayed_cleanup_patterns = [
        'تنظيف البيانات المؤقتة بعد إرسال التفاصيل',
        'إصلاح: تنظيف single_card_successful_usernames فقط إذا لم تكن هناك حاجة لحذف الكروت المرسلة بنجاح',
        'if not (failed_count > 0 and success_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\'):',
        'if hasattr(self, \'single_card_successful_usernames\'):',
        'delattr(self, \'single_card_successful_usernames\')',
        'تم تنظيف single_card_successful_usernames بعد إرسال التفاصيل'
    ]
    
    for pattern in delayed_cleanup_patterns:
        if pattern not in func_code:
            print(f"❌ نمط التنظيف المؤجل غير موجود: {pattern}")
            return False
        print(f"✅ نمط التنظيف المؤجل موجود: {pattern[:50]}...")
    
    return True

def test_delete_function_correctness():
    """اختبار صحة دالة الحذف"""
    print("\n🔍 اختبار صحة دالة الحذف...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة delete_successful_cards_from_mikrotik
    func_match = re.search(r'def delete_successful_cards_from_mikrotik.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة delete_successful_cards_from_mikrotik")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من صحة دالة الحذف
    delete_patterns = [
        'for username in successful_usernames:',
        'api.get_resource(\'/ip/hotspot/user\').remove(numbers=username)',
        'deleted_count += 1',
        'failed_count += 1',
        'success_rate = (deleted_count / len(successful_usernames)) * 100',
        'return deleted_count'
    ]
    
    for pattern in delete_patterns:
        if pattern not in func_code:
            print(f"❌ نمط دالة الحذف غير موجود: {pattern}")
            return False
        print(f"✅ نمط دالة الحذف موجود: {pattern}")
    
    return True

def test_callback_flow():
    """اختبار تدفق معالجة callback"""
    print("\n🔍 اختبار تدفق معالجة callback...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من تدفق callback كامل
    callback_flow_patterns = [
        # 1. إنشاء الزر
        '"callback_data": f"single_card_delete_successful_{success_count}"',
        # 2. معالجة callback الأولي
        'elif callback_data.startswith("single_card_delete_successful_"):',
        'self.handle_single_card_delete_successful_request(bot_token, chat_id, success_count)',
        # 3. إنشاء زر التأكيد
        '"callback_data": f"single_card_delete_successful_confirm_{cards_to_delete}"',
        # 4. معالجة التأكيد
        'if callback_data.startswith("single_card_delete_successful_confirm_"):',
        'self.execute_single_card_delete_successful(bot_token, chat_id, cards_count)',
        # 5. معالجة الإلغاء
        'elif callback_data == "single_card_delete_successful_cancel":',
        'self.cancel_single_card_delete_successful(bot_token, chat_id)'
    ]
    
    for pattern in callback_flow_patterns:
        if pattern not in content:
            print(f"❌ نمط تدفق callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط تدفق callback موجود: {pattern[:50]}...")
    
    return True

def test_data_persistence():
    """اختبار استمرارية البيانات"""
    print("\n🔍 اختبار استمرارية البيانات...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من أن البيانات تبقى متاحة عند الحاجة
    persistence_patterns = [
        # حفظ البيانات أثناء الإرسال
        'self.single_card_successful_usernames.append(cred_username)',
        # حفظ معلومات إضافية عند وجود فشل
        'self.single_card_successful_cards = self.single_card_successful_usernames.copy()',
        # التحقق من وجود البيانات في معالج الحذف
        'if not hasattr(self, \'single_card_successful_cards\') or not self.single_card_successful_cards:',
        # استخدام البيانات في الحذف
        'deleted_count = self.delete_successful_cards_from_mikrotik(self.single_card_successful_cards, api)'
    ]
    
    for pattern in persistence_patterns:
        if pattern not in content:
            print(f"❌ نمط استمرارية البيانات غير موجود: {pattern}")
            return False
        print(f"✅ نمط استمرارية البيانات موجود: {pattern[:50]}...")
    
    return True

def test_error_handling():
    """اختبار معالجة الأخطاء"""
    print("\n🔍 اختبار معالجة الأخطاء...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة الأخطاء
    error_handling_patterns = [
        'لا توجد معلومات محفوظة عن الكروت المرسلة بنجاح',
        'قد تكون العملية قديمة أو تم إعادة تشغيل البرنامج',
        'لا توجد معلومات عن الكروت المطلوب حذفها',
        'خطأ في معالجة callback حذف الكروت المرسلة بنجاح للكرت الواحد',
        'خطأ في تنفيذ حذف الكروت المرسلة بنجاح للكرت الواحد'
    ]
    
    for pattern in error_handling_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة الأخطاء غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة الأخطاء موجود: {pattern[:50]}...")
    
    return True

def run_single_card_delete_button_fix_test():
    """تشغيل اختبار إصلاح زر حذف الكروت الناجحة"""
    print("🚀 بدء اختبار إصلاح زر حذف الكروت الناجحة في الكرت الواحد\n")
    
    tests = [
        test_early_cleanup_fix,
        test_delayed_cleanup,
        test_delete_function_correctness,
        test_callback_flow,
        test_data_persistence,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات الإصلاح نجحت!")
        print("✅ تم إصلاح مشكلة زر حذف الكروت الناجحة بنجاح")
        print("\n🎯 ملخص الإصلاح:")
        print("   • تم إزالة التنظيف المبكر لـ single_card_successful_usernames")
        print("   • تم إضافة تنظيف مؤجل بعد إرسال التفاصيل إلى التلجرام")
        print("   • تم التأكد من صحة دالة الحذف من MikroTik")
        print("   • تم التأكد من تدفق معالجة callback الكامل")
        print("   • تم ضمان استمرارية البيانات عند الحاجة")
        print("   • تم تحسين معالجة الأخطاء")
        print("\n💡 النتيجة المتوقعة:")
        print("   • زر حذف الكروت الناجحة سيصبح تفاعلياً")
        print("   • ستظهر رسالة تأكيد عند الضغط على الزر")
        print("   • سيتم حذف الكروت الناجحة من MikroTik بنجاح")
        print("   • ستظهر تقارير مفصلة عن عملية الحذف")
        return True
    else:
        print("⚠️ بعض اختبارات الإصلاح فشلت")
        return False

if __name__ == "__main__":
    run_single_card_delete_button_fix_test()
