#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وجود الدوال في الملف
"""

import re

def check_functions_in_file():
    """فحص وجود الدوال الجديدة في الملف"""
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن الدوال
    functions_to_check = [
        'def show_delete_confirmation(',
        'def execute_delete_successful_cards(',
        'def cancel_delete_successful_cards('
    ]
    
    print("🔍 فحص وجود الدوال في الملف...")
    
    for func in functions_to_check:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func} - غير موجودة")
    
    # فحص المسافات البادئة (indentation)
    print(f"\n🔍 فحص المسافات البادئة للدوال...")
    
    for func in functions_to_check:
        matches = re.finditer(re.escape(func), content)
        for match in matches:
            # الحصول على السطر الذي يحتوي على الدالة
            start = content.rfind('\n', 0, match.start()) + 1
            end = content.find('\n', match.end())
            line = content[start:end]
            
            # حساب المسافات البادئة
            indent = len(line) - len(line.lstrip())
            print(f"   {func}: {indent} مسافة بادئة")
            
            if indent == 4:
                print(f"   ✅ المسافة البادئة صحيحة (داخل الكلاس)")
            elif indent == 0:
                print(f"   ❌ المسافة البادئة خاطئة (خارج الكلاس)")
            else:
                print(f"   ⚠️ المسافة البادئة غير متوقعة")
    
    # فحص موقع الدوال بالنسبة لـ if __name__ == "__main__":
    print(f"\n🔍 فحص موقع الدوال...")
    
    main_pos = content.find('if __name__ == "__main__":')
    if main_pos == -1:
        print("❌ لم يتم العثور على if __name__ == '__main__':")
        return
    
    for func in functions_to_check:
        func_pos = content.find(func)
        if func_pos != -1:
            if func_pos < main_pos:
                print(f"✅ {func}: قبل if __name__ (صحيح)")
            else:
                print(f"❌ {func}: بعد if __name__ (خطأ)")
        else:
            print(f"❌ {func}: غير موجودة")
    
    # فحص معالج callback
    print(f"\n🔍 فحص معالج callback...")
    
    callback_pattern = 'elif callback_data.startswith("delete_successful_single_"):'
    if callback_pattern in content:
        print(f"✅ معالج callback موجود")
        
        # فحص استدعاء الدوال
        function_calls = [
            'self.show_delete_confirmation(',
            'self.execute_delete_successful_cards(',
            'self.cancel_delete_successful_cards('
        ]
        
        for call in function_calls:
            if call in content:
                print(f"✅ استدعاء: {call}")
            else:
                print(f"❌ استدعاء مفقود: {call}")
    else:
        print(f"❌ معالج callback غير موجود")

if __name__ == "__main__":
    check_functions_in_file()
