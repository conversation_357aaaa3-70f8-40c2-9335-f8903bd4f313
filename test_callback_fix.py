#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح معالج callback
"""

import re

def test_callback_fix():
    """اختبار إصلاح معالج callback"""
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔍 اختبار إصلاح معالج callback...")
    
    # 1. التحقق من وجود المعالج الجديد فقط
    new_handler_pattern = r'elif callback_data\.startswith\("delete_successful_single_"\):'
    matches = re.findall(new_handler_pattern, content)
    
    print(f"\n📋 عدد معالجات delete_successful_single_: {len(matches)}")
    
    if len(matches) == 1:
        print("✅ يوجد معالج واحد فقط (صحيح)")
    elif len(matches) > 1:
        print("❌ يوجد أكثر من معالج (تضارب)")
        return False
    else:
        print("❌ لا يوجد معالج")
        return False
    
    # 2. التحقق من استدعاء الدوال الصحيحة
    function_calls = [
        'self.show_delete_confirmation(bot_token, chat_id, cards_count)',
        'self.execute_delete_successful_cards(bot_token, chat_id, cards_count)',
        'self.cancel_delete_successful_cards(bot_token, chat_id)'
    ]
    
    print(f"\n📋 فحص استدعاء الدوال:")
    for call in function_calls:
        if call in content:
            print(f"✅ {call[:40]}...")
        else:
            print(f"❌ {call[:40]}... - غير موجود")
            return False
    
    # 3. التحقق من عدم وجود استدعاءات للدوال القديمة
    old_function_calls = [
        'self.handle_delete_successful_single_request(',
        'self.execute_delete_successful_single(',
        'self.cancel_delete_successful_single('
    ]
    
    print(f"\n📋 فحص عدم وجود استدعاءات قديمة:")
    for call in old_function_calls:
        if call in content:
            print(f"❌ {call[:40]}... - لا يزال موجود")
            return False
        else:
            print(f"✅ {call[:40]}... - تم إزالته")
    
    # 4. التحقق من وجود الدوال الجديدة
    new_functions = [
        'def show_delete_confirmation(',
        'def execute_delete_successful_cards(',
        'def cancel_delete_successful_cards('
    ]
    
    print(f"\n📋 فحص وجود الدوال الجديدة:")
    for func in new_functions:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func} - غير موجودة")
            return False
    
    # 5. التحقق من callback_data في الأزرار
    button_callbacks = [
        '"callback_data": f"delete_successful_single_{success_count}"',
        '"callback_data": f"delete_successful_single_confirm_{len(single_card_data)}"',
        '"callback_data": "delete_successful_single_cancel"'
    ]
    
    print(f"\n📋 فحص callback_data في الأزرار:")
    for callback in button_callbacks:
        if callback in content:
            print(f"✅ {callback[:50]}...")
        else:
            print(f"❌ {callback[:50]}... - غير موجود")
            return False
    
    print(f"\n🎉 جميع الفحوصات نجحت!")
    print(f"✅ تم إصلاح معالج callback بنجاح")
    print(f"✅ الدوال الجديدة موجودة ومتصلة بشكل صحيح")
    print(f"✅ تم إزالة المعالجات والدوال القديمة")
    
    return True

if __name__ == "__main__":
    test_callback_fix()
