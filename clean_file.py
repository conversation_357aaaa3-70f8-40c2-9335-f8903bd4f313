#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف الملف من الدوال المحذوفة
"""

def clean_file():
    input_file = "اخر حاجة  - كروت وبوت.py"
    output_file = "اخر حاجة  - كروت وبوت_clean.py"
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # الاحتفاظ بالأسطر حتى السطر 25597 فقط
    clean_lines = lines[:25597]
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(clean_lines)
    
    print(f"تم تنظيف الملف: {len(clean_lines)} سطر")
    print(f"الملف النظيف: {output_file}")

if __name__ == "__main__":
    clean_file()
