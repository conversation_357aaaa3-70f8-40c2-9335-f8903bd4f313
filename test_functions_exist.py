#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وجود الدوال الجديدة
"""

import sys
import os

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_functions_exist():
    """اختبار وجود الدوال الجديدة في الكلاس"""
    try:
        # استيراد الكلاس
        from اخر_حاجة___كروت_وبوت import MikroTikCardGenerator
        
        # إنشاء كائن من الكلاس
        generator = MikroTikCardGenerator()
        
        # اختبار وجود الدوال
        functions_to_test = [
            'show_delete_confirmation',
            'execute_delete_successful_cards', 
            'cancel_delete_successful_cards'
        ]
        
        print("🔍 اختبار وجود الدوال الجديدة...")
        
        for func_name in functions_to_test:
            if hasattr(generator, func_name):
                func = getattr(generator, func_name)
                if callable(func):
                    print(f"✅ {func_name}: موجودة وقابلة للاستدعاء")
                else:
                    print(f"❌ {func_name}: موجودة ولكن غير قابلة للاستدعاء")
            else:
                print(f"❌ {func_name}: غير موجودة")
        
        # اختبار معالج callback
        print(f"\n🔍 اختبار معالج callback...")
        if hasattr(generator, 'process_telegram_callback'):
            print("✅ process_telegram_callback: موجودة")
        else:
            print("❌ process_telegram_callback: غير موجودة")
        
        print(f"\n📊 النتيجة: الدوال موجودة في الكلاس")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

if __name__ == "__main__":
    test_functions_exist()
