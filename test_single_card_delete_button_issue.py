#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مشكلة زر حذف الكروت الناجحة في الكرت الواحد
"""

import re
import os

def test_single_card_successful_cards_saving():
    """اختبار حفظ الكروت الناجحة للكرت الواحد"""
    print("🔍 اختبار حفظ الكروت الناجحة للكرت الواحد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من حفظ الكروت الناجحة أثناء الإرسال
    saving_patterns = [
        'if not hasattr(self, \'single_card_successful_usernames\'):',
        'self.single_card_successful_usernames = []',
        'self.single_card_successful_usernames.append(cred_username)',
        'حفظ اسم المستخدم الناجح للكرت الواحد'
    ]
    
    for pattern in saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ الكروت الناجحة أثناء الإرسال غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ الكروت الناجحة أثناء الإرسال موجود: {pattern}")
    
    # التحقق من حفظ معلومات الكروت الناجحة عند وجود فشل
    info_saving_patterns = [
        'حفظ الكروت الناجحة لخيار "حذف الكروت المرسلة بنجاح"',
        'if failed_count > 0 and success_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':',
        'if hasattr(self, \'single_card_successful_usernames\') and self.single_card_successful_usernames:',
        'self.single_card_successful_cards = self.single_card_successful_usernames.copy()',
        'self.single_card_successful_cards_info = {'
    ]
    
    for pattern in info_saving_patterns:
        if pattern not in func_code:
            print(f"❌ نمط حفظ معلومات الكروت الناجحة غير موجود: {pattern}")
            return False
        print(f"✅ نمط حفظ معلومات الكروت الناجحة موجود: {pattern}")
    
    return True

def test_delete_button_display_conditions():
    """اختبار شروط إظهار زر حذف الكروت الناجحة"""
    print("\n🔍 اختبار شروط إظهار زر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من شروط إظهار الزر
    button_conditions = [
        'show_delete_successful_button = (',
        'failed_count > 0 and',
        'success_count > 0 and',
        'hasattr(self, \'single_card_successful_cards\') and',
        'bool(self.single_card_successful_cards)'
    ]
    
    for pattern in button_conditions:
        if pattern not in func_code:
            print(f"❌ شرط إظهار الزر غير موجود: {pattern}")
            return False
        print(f"✅ شرط إظهار الزر موجود: {pattern}")
    
    # التحقق من إنشاء الزر
    button_creation_patterns = [
        'if show_delete_successful_button:',
        'keyboard_buttons.append([',
        '"text": f"🗑️ حذف الكروت المرسلة بنجاح ({success_count})"',
        '"callback_data": f"single_card_delete_successful_{success_count}"'
    ]
    
    for pattern in button_creation_patterns:
        if pattern not in func_code:
            print(f"❌ نمط إنشاء الزر غير موجود: {pattern}")
            return False
        print(f"✅ نمط إنشاء الزر موجود: {pattern}")
    
    return True

def test_callback_handling():
    """اختبار معالجة callback للزر"""
    print("\n🔍 اختبار معالجة callback للزر...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالجة callback
    callback_patterns = [
        'elif callback_data.startswith("single_card_delete_successful_"):',
        'معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد',
        'if callback_data.startswith("single_card_delete_successful_confirm_"):',
        'elif callback_data == "single_card_delete_successful_cancel":',
        'self.handle_single_card_delete_successful_request(bot_token, chat_id, success_count)'
    ]
    
    for pattern in callback_patterns:
        if pattern not in content:
            print(f"❌ نمط معالجة callback غير موجود: {pattern}")
            return False
        print(f"✅ نمط معالجة callback موجود: {pattern}")
    
    return True

def test_handler_functions():
    """اختبار وجود دوال المعالجة"""
    print("\n🔍 اختبار وجود دوال المعالجة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال
    handler_functions = [
        'def handle_single_card_delete_successful_request(',
        'def execute_single_card_delete_successful(',
        'def cancel_single_card_delete_successful('
    ]
    
    for func in handler_functions:
        if func not in content:
            print(f"❌ دالة المعالجة غير موجودة: {func}")
            return False
        print(f"✅ دالة المعالجة موجودة: {func}")
    
    return True

def test_delete_function():
    """اختبار دالة الحذف من MikroTik"""
    print("\n🔍 اختبار دالة الحذف من MikroTik...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دالة الحذف
    if 'def delete_successful_cards_from_mikrotik(' not in content:
        print("❌ دالة delete_successful_cards_from_mikrotik غير موجودة")
        return False
    
    print("✅ دالة delete_successful_cards_from_mikrotik موجودة")
    
    # البحث عن الدالة
    func_match = re.search(r'def delete_successful_cards_from_mikrotik.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على محتوى دالة delete_successful_cards_from_mikrotik")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من محتوى الدالة
    delete_patterns = [
        'for username in successful_usernames:',
        'users = api.get_resource(\'/ip/hotspot/user\')',
        'user_list = users.get()',
        'users.remove(id=user_id)',
        'deleted_count += 1'
    ]
    
    for pattern in delete_patterns:
        if pattern not in func_code:
            print(f"❌ نمط الحذف غير موجود: {pattern}")
            return False
        print(f"✅ نمط الحذف موجود: {pattern}")
    
    return True

def test_potential_issues():
    """اختبار المشاكل المحتملة"""
    print("\n🔍 اختبار المشاكل المحتملة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues_found = []
    
    # 1. التحقق من تنظيف البيانات المبكر
    if 'delattr(self, \'single_card_successful_usernames\')' in content:
        # البحث عن مكان التنظيف
        cleanup_matches = re.findall(r'delattr\(self, \'single_card_successful_usernames\'\)', content)
        print(f"⚠️ تم العثور على {len(cleanup_matches)} مكان لحذف single_card_successful_usernames")
        
        # التحقق من التوقيت
        send_details_pos = content.find('def send_single_card_details_to_telegram')
        cleanup_positions = []
        for match in re.finditer(r'delattr\(self, \'single_card_successful_usernames\'\)', content):
            cleanup_positions.append(match.start())
        
        early_cleanup = [pos for pos in cleanup_positions if pos < send_details_pos]
        if early_cleanup:
            issues_found.append("تنظيف مبكر لـ single_card_successful_usernames قبل إرسال التفاصيل")
    
    # 2. التحقق من شروط الحفظ
    save_condition = 'if failed_count > 0 and success_count > 0 and getattr(self, \'system_type\', \'\') == \'hotspot\':'
    if save_condition not in content:
        issues_found.append("شرط حفظ الكروت الناجحة غير موجود")
    
    # 3. التحقق من تسلسل العمليات
    send_to_mikrotik_pos = content.find('def send_single_card_to_mikrotik_silent')
    send_details_pos = content.find('def send_single_card_details_to_telegram')
    
    if send_to_mikrotik_pos > send_details_pos:
        issues_found.append("ترتيب الدوال قد يكون مشكلة")
    
    if issues_found:
        print("⚠️ مشاكل محتملة تم اكتشافها:")
        for issue in issues_found:
            print(f"   - {issue}")
        return False
    else:
        print("✅ لم يتم اكتشاف مشاكل واضحة")
        return True

def run_single_card_delete_button_test():
    """تشغيل اختبار مشكلة زر حذف الكروت الناجحة"""
    print("🚀 بدء اختبار مشكلة زر حذف الكروت الناجحة في الكرت الواحد\n")
    
    tests = [
        test_single_card_successful_cards_saving,
        test_delete_button_display_conditions,
        test_callback_handling,
        test_handler_functions,
        test_delete_function,
        test_potential_issues
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ الكود يبدو صحيحاً من الناحية النظرية")
        print("\n💡 إذا كان الزر غير تفاعلي، فقد تكون المشكلة في:")
        print("   • توقيت تنظيف البيانات")
        print("   • شروط الحفظ لا تتحقق في الواقع")
        print("   • مشكلة في إرسال الرسالة مع الأزرار")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج إصلاح")
        return False

if __name__ == "__main__":
    run_single_card_delete_button_test()
