#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق الجديد لزر حذف الكروت الناجحة
"""

import re

def test_new_data_saving():
    """اختبار آلية حفظ البيانات الجديدة"""
    print("🔍 اختبار آلية حفظ البيانات الجديدة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_to_mikrotik_silent
    func_match = re.search(r'def send_single_card_to_mikrotik_silent.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_to_mikrotik_silent")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من آلية الحفظ الجديدة
    new_saving_patterns = [
        'حفظ بيانات الكرت الناجح - نسخة محسنة',
        'if not hasattr(self, \'successful_cards_data\'):',
        'self.successful_cards_data = []',
        'card_data = {',
        '\'username\': cred_username,',
        '\'operation_type\': \'single_card\',',
        '\'system_type\': \'hotspot\',',
        'self.successful_cards_data.append(card_data)'
    ]
    
    print("\n📋 فحص آلية الحفظ الجديدة:")
    for pattern in new_saving_patterns:
        if pattern in func_code:
            print(f"✅ {pattern[:50]}...")
        else:
            print(f"❌ {pattern[:50]}... - غير موجود")
            return False
    
    # التحقق من التنظيف
    cleanup_patterns = [
        'تنظيف بيانات العمليات السابقة',
        'if hasattr(self, \'successful_cards_data\'):',
        'self.successful_cards_data = [card for card in self.successful_cards_data',
        'if card.get(\'operation_type\') != \'single_card\']'
    ]
    
    print("\n📋 فحص آلية التنظيف:")
    for pattern in cleanup_patterns:
        if pattern in func_code:
            print(f"✅ {pattern[:50]}...")
        else:
            print(f"❌ {pattern[:50]}... - غير موجود")
            return False
    
    return True

def test_new_button_conditions():
    """اختبار شروط إظهار الزر الجديدة"""
    print("\n🔍 اختبار شروط إظهار الزر الجديدة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من الشروط الجديدة
    new_conditions = [
        'شروط إظهار زر حذف الكروت الناجحة - نسخة محسنة',
        'show_delete_successful_button = (',
        'failed_count > 0 and  # يوجد كروت فاشلة',
        'success_count > 0 and  # يوجد كروت ناجحة',
        'getattr(self, \'system_type\', \'\') == \'hotspot\' and  # نظام HotSpot',
        'hasattr(self, \'successful_cards_data\') and  # البيانات محفوظة',
        'len(self.successful_cards_data) > 0  # البيانات غير فارغة'
    ]
    
    print("\n📋 فحص الشروط الجديدة:")
    for condition in new_conditions:
        if condition in func_code:
            print(f"✅ {condition[:50]}...")
        else:
            print(f"❌ {condition[:50]}... - غير موجود")
            return False
    
    return True

def test_new_button_creation():
    """اختبار إنشاء الزر الجديد"""
    print("\n🔍 اختبار إنشاء الزر الجديد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # البحث عن دالة send_single_card_details_to_telegram
    func_match = re.search(r'def send_single_card_details_to_telegram.*?(?=def|\Z)', content, re.DOTALL)
    if not func_match:
        print("❌ لم يتم العثور على دالة send_single_card_details_to_telegram")
        return False
    
    func_code = func_match.group(0)
    
    # التحقق من النص والزر
    button_patterns = [
        '🗑️ <b>حذف الكروت الناجحة:</b>',
        'يمكنك حذف الـ',
        'مع الاحتفاظ بالكروت الفاشلة لإعادة المحاولة',
        'if show_delete_successful_button:',
        '"text": f"🗑️ حذف الكروت الناجحة ({success_count})"',
        '"callback_data": f"delete_successful_single_{success_count}"'
    ]
    
    print("\n📋 فحص النص والزر:")
    for pattern in button_patterns:
        if pattern in func_code:
            print(f"✅ {pattern[:50]}...")
        else:
            print(f"❌ {pattern[:50]}... - غير موجود")
            return False
    
    return True

def test_new_callback_handler():
    """اختبار معالج callback الجديد"""
    print("\n🔍 اختبار معالج callback الجديد...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من معالج callback
    callback_patterns = [
        'معالج callback لحذف الكروت الناجحة - نسخة محسنة',
        'elif callback_data.startswith("delete_successful_single_"):',
        'معالجة طلب حذف الكروت الناجحة',
        'if callback_data.startswith("delete_successful_single_confirm_"):',
        'elif callback_data == "delete_successful_single_cancel":',
        'self.execute_delete_successful_cards(bot_token, chat_id, cards_count)',
        'self.cancel_delete_successful_cards(bot_token, chat_id)',
        'self.show_delete_confirmation(bot_token, chat_id, cards_count)'
    ]
    
    print("\n📋 فحص معالج callback:")
    for pattern in callback_patterns:
        if pattern in content:
            print(f"✅ {pattern[:50]}...")
        else:
            print(f"❌ {pattern[:50]}... - غير موجود")
            return False
    
    return True

def test_new_handler_functions():
    """اختبار دوال المعالجة الجديدة"""
    print("\n🔍 اختبار دوال المعالجة الجديدة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود الدوال الثلاث
    handler_functions = [
        'def show_delete_confirmation(',
        'def execute_delete_successful_cards(',
        'def cancel_delete_successful_cards('
    ]
    
    print("\n📋 فحص دوال المعالجة:")
    for func in handler_functions:
        if func in content:
            print(f"✅ {func}")
        else:
            print(f"❌ {func} - غير موجودة")
            return False
    
    # التحقق من محتوى الدوال
    function_features = [
        # show_delete_confirmation
        'عرض رسالة تأكيد حذف الكروت الناجحة',
        'فلترة الكروت الخاصة بالكرت الواحد فقط',
        'delete_successful_single_confirm_',
        'delete_successful_single_cancel',
        
        # execute_delete_successful_cards
        'تنفيذ حذف الكروت الناجحة من MikroTik',
        'api.get_resource(\'/ip/hotspot/user\').remove(numbers=username)',
        'تنظيف البيانات المحلية',
        'معدل النجاح',
        
        # cancel_delete_successful_cards
        'إلغاء عملية حذف الكروت الناجحة',
        'تم إلغاء الحذف',
        'لم يتم حذف أي كروت'
    ]
    
    print("\n📋 فحص محتوى الدوال:")
    for feature in function_features:
        if feature in content:
            print(f"✅ {feature[:50]}...")
        else:
            print(f"❌ {feature[:50]}... - غير موجود")
            return False
    
    return True

def test_improved_features():
    """اختبار الميزات المحسنة"""
    print("\n🔍 اختبار الميزات المحسنة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من الميزات المحسنة
    improved_features = [
        # بيانات أكثر تفصيلاً
        '\'timestamp\': time.time()',
        '\'password\': cred.get("password", "")',
        '\'profile\': cred.get("profile", "")',
        
        # فلترة ذكية
        'if card.get(\'operation_type\') == \'single_card\'',
        'single_card_data = [card for card in',
        
        # رسائل واضحة
        'لا توجد بيانات كروت ناجحة محفوظة',
        'لا توجد كروت ناجحة من عملية الكرت الواحد',
        'سيتم حذف الكروت من خادم MikroTik نهائياً',
        
        # إحصائيات مفصلة
        'success_rate = (deleted_count / len(usernames_to_delete)) * 100',
        'تم حذف: {deleted_count} كرت',
        'فشل حذف: {failed_count} كرت',
        
        # تنظيف ذكي
        'تنظيف بيانات العمليات السابقة',
        'إزالة بيانات الكرت الواحد السابقة فقط'
    ]
    
    print("\n📋 فحص الميزات المحسنة:")
    for feature in improved_features:
        if feature in content:
            print(f"✅ {feature[:50]}...")
        else:
            print(f"❌ {feature[:50]}... - غير موجود")
            return False
    
    return True

def run_new_implementation_test():
    """تشغيل اختبار التطبيق الجديد"""
    print("🚀 بدء اختبار التطبيق الجديد لزر حذف الكروت الناجحة\n")
    
    tests = [
        test_new_data_saving,
        test_new_button_conditions,
        test_new_button_creation,
        test_new_callback_handler,
        test_new_handler_functions,
        test_improved_features
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ الاختبار نجح\n")
            else:
                print("❌ الاختبار فشل\n")
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}\n")
    
    print(f"📊 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات التطبيق الجديد نجحت!")
        print("✅ تم إعادة إنشاء زر حذف الكروت الناجحة بنجاح")
        print("\n🎯 الميزات الجديدة:")
        print("   • آلية حفظ بيانات محسنة ومفصلة")
        print("   • شروط إظهار زر واضحة ومبسطة")
        print("   • معالجة callback مباشرة وفعالة")
        print("   • رسائل تأكيد واضحة ومفهومة")
        print("   • تقارير مفصلة عن نتائج الحذف")
        print("   • تنظيف ذكي للبيانات")
        print("   • فلترة دقيقة للكروت")
        print("   • معالجة أخطاء شاملة")
        return True
    else:
        print("⚠️ بعض اختبارات التطبيق الجديد فشلت")
        return False

if __name__ == "__main__":
    run_new_implementation_test()
