#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من إزالة زر حذف الكروت الناجحة
"""

import re

def test_removal_verification():
    """التحقق من إزالة جميع المكونات المتعلقة بزر حذف الكروت الناجحة"""
    print("🔍 التحقق من إزالة زر حذف الكروت الناجحة...")
    
    main_file = "اخر حاجة  - كروت وبوت.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # قائمة الأنماط التي يجب ألا تكون موجودة
    removed_patterns = [
        # شروط إظهار الزر
        'show_delete_successful_button = (',
        'hasattr(self, \'single_card_successful_cards\') and',
        'bool(self.single_card_successful_cards)',
        
        # النص المتعلق بالزر
        '🗑️ <b>حذف الكروت المرسلة بنجاح:</b>',
        'يمكنك اختيار حذف الـ',
        
        # إنشاء الزر
        '"text": f"🗑️ حذف الكروت المرسلة بنجاح',
        '"callback_data": f"single_card_delete_successful_',
        
        # معالج callback
        'elif callback_data.startswith("single_card_delete_successful_"):',
        'معالجة callback لحذف الكروت المرسلة بنجاح للكرت الواحد',
        
        # دوال المعالجة
        'def handle_single_card_delete_successful_request(',
        'def execute_single_card_delete_successful(',
        'def cancel_single_card_delete_successful(',
        
        # متغيرات البيانات (في السياق الفعلي)
        'self.single_card_successful_usernames.append(',
        'self.single_card_successful_cards = self.single_card_successful_usernames.copy()',
        'self.single_card_successful_cards_info = {',
        
        # رسائل التأكيد
        'تأكيد حذف الكروت المرسلة بنجاح',
        'single_card_delete_successful_confirm_',
        'single_card_delete_successful_cancel'
    ]
    
    # الأنماط التي يجب أن تكون موجودة (تشير إلى الإزالة)
    removal_indicators = [
        'تم إزالة زر حذف الكروت الناجحة',
        'سيتم إعادة إنشاءه من الصفر',
        'تم الإزالة',
        'تم إزالة حفظ اسم المستخدم الناجح',
        'تم إزالة حفظ الكروت الناجحة',
        'تم إزالة معالج callback لحذف الكروت الناجحة',
        'تم إزالة التسجيل المفصل'
    ]
    
    print("\n📋 فحص إزالة الأنماط المطلوبة:")
    removed_count = 0
    for pattern in removed_patterns:
        if pattern in content:
            print(f"❌ لا يزال موجود: {pattern[:50]}...")
        else:
            print(f"✅ تم إزالته: {pattern[:50]}...")
            removed_count += 1
    
    print(f"\n📊 تم إزالة {removed_count}/{len(removed_patterns)} نمط")
    
    print("\n📋 فحص مؤشرات الإزالة:")
    indicators_found = 0
    for indicator in removal_indicators:
        if indicator in content:
            print(f"✅ موجود: {indicator}")
            indicators_found += 1
        else:
            print(f"❌ غير موجود: {indicator}")
    
    print(f"\n📊 تم العثور على {indicators_found}/{len(removal_indicators)} مؤشر إزالة")
    
    # فحص عدد الأسطر
    lines = content.split('\n')
    print(f"\n📏 عدد الأسطر في الملف: {len(lines)}")
    
    # التحقق من نهاية الملف
    last_lines = lines[-5:]
    print(f"\n📄 آخر 5 أسطر:")
    for i, line in enumerate(last_lines, len(lines)-4):
        print(f"   {i}: {line}")
    
    # النتيجة النهائية
    if removed_count == len(removed_patterns) and indicators_found >= 5:
        print("\n🎉 تم إزالة زر حذف الكروت الناجحة بنجاح!")
        print("✅ جميع المكونات المتعلقة بالزر تم إزالتها")
        print("✅ مؤشرات الإزالة موجودة")
        print("✅ الملف جاهز لإعادة الإنشاء من الصفر")
        return True
    else:
        print("\n⚠️ الإزالة غير مكتملة")
        print(f"   - أنماط متبقية: {len(removed_patterns) - removed_count}")
        print(f"   - مؤشرات مفقودة: {len(removal_indicators) - indicators_found}")
        return False

if __name__ == "__main__":
    test_removal_verification()
